"""
Utility module for triggering followup task generation.
This module is designed to avoid circular imports by providing a simple interface
for triggering followup generation from the management service.
"""

import asyncio
from typing import Optional
from bson import ObjectId


async def trigger_followup_generation(
    task_set_id: str, 
    user_id: str, 
    audio_data: bytes, 
    tenant_db,
    logger
) -> bool:
    """
    Trigger followup task generation for a completed task set.
    
    Args:
        task_set_id: The completed task set ID
        user_id: The user ID
        audio_data: The original audio data
        tenant_db: The tenant database connection
        logger: Logger instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Import the followup generation function dynamically to avoid circular imports
        from app.v2.api.socket_service_v2.generator.folllowup import followup_generate
        
        # Call the followup generation function
        await followup_generate(task_set_id, user_id, audio_data, tenant_db)
        logger.info(f"Successfully triggered followup generation for task set {task_set_id}")
        return True
        
    except ImportError as e:
        logger.error(f"Failed to import followup generation module: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in followup generation for task set {task_set_id}: {e}")
        return False


def can_trigger_followup(task_set: dict) -> bool:
    """
    Check if a task set is eligible for followup generation.
    
    Args:
        task_set: The task set document
        
    Returns:
        bool: True if eligible for followup generation
    """
    # Check if this is a primary task set (not already a followup)
    gen_type = task_set.get("gentype", "primary")
    is_followup = task_set.get("is_followup", False)
    
    # Only generate followups for primary task sets
    if gen_type != "primary" or is_followup:
        return False
        
    # Check if we have the required data
    user_id = task_set.get("user_id")
    input_content = task_set.get("input_content", {})
    audio_data = input_content.get("audio_data")
    
    return bool(user_id and audio_data)
